
import os
import torch
import logging
from vllm import LLM, SamplingParams

# 设置详细日志
# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger(__name__)

def debug_model_loading():
    """调试模型加载和kernel调用流程"""

    # 断点1: 开始模型初始化
    print("=== 开始模型初始化 ===")

    model_name = "/home/<USER>/single_llama"
    # Qwen-QwQ-32B-W8A8-SmoothQuant Meta-Llama-3-8B-Instruct-GPTQ-Int8

    # 这里设置第一个断点 - 模型初始化入口
    # 根据可用GPU数量动态生成配置
    num_available_gpus = 4

    configs = []

    # 如果有多张GPU，优先尝试多GPU配置
    # if num_available_gpus >= 2:
    #     configs.extend([
    #         {
    #             "name": f"多GPU配置 ({min(num_available_gpus, 4)}张卡)",
    #             "tensor_parallel_size": min(num_available_gpus, 4),
    #             "gpu_memory_utilization": 0.85,
    #             "max_model_len": 1024,
    #             "enable_prefix_caching": True,
    #         },
    #         {
    #             "name": f"保守多GPU配置 ({min(num_available_gpus, 2)}张卡)",
    #             "tensor_parallel_size": min(num_available_gpus, 2),
    #             "gpu_memory_utilization": 0.75,
    #             "max_model_len": 512,
    #             "enable_prefix_caching": False,
    #         }
    #     ])

    # 添加单GPU配置作为备选
    configs.extend([
        {
            "name": "单GPU配置1: 中等内存利用率",
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.85,
            "max_model_len": 512,
            "enable_prefix_caching": True,
        },
        {
            "name": "单GPU配置2: 保守内存利用率",
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.75,
            "max_model_len": 256,
            "enable_prefix_caching": False,
        },
        {
            "name": "单GPU配置3: 最小内存利用率",
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.65,
            "max_model_len": 128,
            "enable_prefix_caching": False,
        },
        {
            "name": "单GPU配置4: 极小内存利用率",
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.55,
            "max_model_len": 64,
            "enable_prefix_caching": False,
        }
    ])

    llm = None
    successful_config = None

    for config in configs:
        print(f"=== 尝试{config['name']} ===")
        print(f"tensor_parallel_size: {config['tensor_parallel_size']}")
        print(f"gpu_memory_utilization: {config['gpu_memory_utilization']}")
        print(f"max_model_len: {config['max_model_len']}")

        try:
            # 清理GPU内存
            torch.cuda.empty_cache()

            # 如果使用多GPU，设置可见的GPU
            if config['tensor_parallel_size'] > 1:
                visible_gpus = list(range(config['tensor_parallel_size']))
                os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(map(str, visible_gpus))
                print(f"使用GPU: {visible_gpus}")

            llm = LLM(
                model=model_name,
                # quantization="gptq",
                tensor_parallel_size=config["tensor_parallel_size"],
                max_model_len=config["max_model_len"],
                enable_prefix_caching=config["enable_prefix_caching"],
                trust_remote_code=True,
                gpu_memory_utilization=config["gpu_memory_utilization"],
                # enforce_eager=True,  # 对多GPU更稳定
            )
            print(f"✅ {config['name']}成功！")
            successful_config = config
            break
        except Exception as e:
            print(f"❌ {config['name']}失败: {e}")
            # 清理内存后继续尝试下一个配置
            torch.cuda.empty_cache()
            continue

    if llm is None:
        raise RuntimeError("所有配置都失败了，无法加载模型")
    
    print("=== 模型加载完成 ===")
    if successful_config:
        print(f"成功配置: {successful_config['name']}")
        print(f"使用GPU数量: {successful_config['tensor_parallel_size']}")
        print(f"内存利用率: {successful_config['gpu_memory_utilization']}")
        print(f"最大序列长度: {successful_config['max_model_len']}")

    # 断点2: 开始推理
    print("=== 开始推理测试 ===")
    prompts = ["Hello, how are you?"]
    sampling_params = SamplingParams(
        temperature=0.0, 
        top_p=1.0,
        max_tokens=20
    )
    
    # 这里设置第二个断点 - 推理入口
    outputs = llm.generate(prompts, sampling_params)
    
    for output in outputs:
        print(f"Prompt: {output.prompt}")
        print(f"Generated: {output.outputs[0].text}")

if __name__ == "__main__":
    debug_model_loading()
